#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C题男胎数据快速处理代码
一键完成：缺失值处理 + 孕周转换 + Y染色体浓度判断
"""

import pandas as pd
import numpy as np
import re

# 读取男胎数据
print("正在读取男胎数据...")
df = pd.read_excel('附件.xlsx', sheet_name='男胎检测数据')
print(f"原始数据: {df.shape[0]}行 × {df.shape[1]}列")

# 1. 缺失值处理（行级删除）
print("\n1. 处理缺失值...")
original_rows = len(df)

# 检测缺失值（NaN、空字符串、'null'）
missing_mask = pd.DataFrame(False, index=df.index, columns=df.columns)
for col in df.columns:
    missing_mask[col] |= df[col].isna()
    if df[col].dtype == 'object':
        missing_mask[col] |= (df[col] == '')
        missing_mask[col] |= (df[col] == 'null')

# 删除包含缺失值的行
rows_to_drop = df[missing_mask.any(axis=1)].index
df_clean = df.drop(rows_to_drop)

print(f"删除了 {len(rows_to_drop)} 行包含缺失值的数据")
print(f"保留数据: {len(df_clean)}行，保留率: {len(df_clean)/original_rows*100:.1f}%")

# 2. 孕周转换（周 → 天）
print("\n2. 转换孕周数据...")

def week_to_days(week_str):
    """将孕周字符串转换为天数"""
    if pd.isna(week_str):
        return np.nan
    
    week_str = str(week_str).strip().upper()
    pattern = r'(\d+)W(?:\+(\d+))?'
    match = re.match(pattern, week_str)
    
    if match:
        weeks = int(match.group(1))
        days = int(match.group(2)) if match.group(2) else 0
        return weeks * 7 + days
    return np.nan

# 转换第9列（检测孕周）
pregnancy_days = df_clean.iloc[:, 9].apply(week_to_days)
df_clean.iloc[:, 9] = pregnancy_days

print(f"孕周转换完成，范围: {pregnancy_days.min():.0f}-{pregnancy_days.max():.0f}天")

# 3. Y染色体浓度正常性判断
print("\n3. 添加Y染色体浓度正常性判断...")

# 4%阈值判断
threshold = 0.04
y_conc = df_clean['Y染色体浓度']
df_clean['Y染色体浓度正常性'] = y_conc.apply(
    lambda x: '正常' if x >= threshold else '不正常'
)

# 统计结果
normal_count = (df_clean['Y染色体浓度正常性'] == '正常').sum()
abnormal_count = (df_clean['Y染色体浓度正常性'] == '不正常').sum()

print(f"正常: {normal_count}例 ({normal_count/len(df_clean)*100:.1f}%)")
print(f"不正常: {abnormal_count}例 ({abnormal_count/len(df_clean)*100:.1f}%)")

# 4. 保存结果
output_file = '男胎数据_处理完成.xlsx'
df_clean.to_excel(output_file, index=False)

print(f"\n✅ 处理完成！")
print(f"输出文件: {output_file}")
print(f"最终数据: {df_clean.shape[0]}行 × {df_clean.shape[1]}列")

# 5. 快速统计报告
print(f"\n📊 快速统计报告:")
print(f"年龄范围: {df_clean['年龄'].min()}-{df_clean['年龄'].max()}岁")
print(f"孕周范围: {df_clean.iloc[:, 9].min():.0f}-{df_clean.iloc[:, 9].max():.0f}天")
print(f"Y染色体浓度范围: {y_conc.min():.4f}-{y_conc.max():.4f}")

# 胎儿健康状况统计
health_stats = df_clean['胎儿是否健康'].value_counts()
print(f"胎儿健康: {health_stats.get('是', 0)}例")
print(f"胎儿异常: {health_stats.get('否', 0)}例")

print("\n🎉 所有处理步骤已完成！")
