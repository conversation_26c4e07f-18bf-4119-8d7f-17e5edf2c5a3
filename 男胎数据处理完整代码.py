#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C题男胎数据清洗和预处理完整代码
功能：
1. 缺失值检测和处理（行级删除）
2. 孕周数据转换（周转天数）
3. Y染色体浓度正常性判断（≥4%为正常）
4. 生成详细的处理报告
"""

import pandas as pd
import numpy as np
import re

def load_and_analyze_data():
    """加载数据并进行初始分析"""
    print("=== 第0步：数据加载和初始分析 ===")
    
    # 读取Excel文件，查看所有工作表
    excel_file = pd.ExcelFile('附件.xlsx')
    print('Excel文件中的工作表：')
    for sheet in excel_file.sheet_names:
        print(f'- {sheet}')
    
    # 读取男胎数据
    df_male = pd.read_excel('附件.xlsx', sheet_name='男胎检测数据')
    print(f'\n男胎数据形状: {df_male.shape}')
    print(f'总行数: {len(df_male)}')
    print(f'总列数: {len(df_male.columns)}')
    
    return df_male

def detect_missing_values(df):
    """检测各种类型的缺失值"""
    print("\n=== 第1步：缺失值检测和处理 ===")
    print(f'处理前数据形状: {df.shape}')
    
    # 检测各种类型的缺失值
    missing_info = {}
    for col in df.columns:
        # 检测NaN值
        nan_count = df[col].isna().sum()
        # 检测空字符串
        empty_str_count = (df[col] == '').sum() if df[col].dtype == 'object' else 0
        # 检测null字符串
        null_str_count = (df[col] == 'null').sum() if df[col].dtype == 'object' else 0
        
        total_missing = nan_count + empty_str_count + null_str_count
        missing_info[col] = {
            'nan': nan_count,
            'empty_str': empty_str_count,
            'null_str': null_str_count,
            'total_missing': total_missing,
            'missing_rate': total_missing / len(df) * 100
        }
    
    # 显示缺失值统计
    print('各列缺失值统计:')
    for col, info in missing_info.items():
        if info['total_missing'] > 0:
            print(f'{col}: {info["total_missing"]} ({info["missing_rate"]:.2f}%) - '
                  f'NaN: {info["nan"]}, 空字符串: {info["empty_str"]}, null: {info["null_str"]}')
    
    # 检查完全为空的列
    completely_empty_cols = []
    for col, info in missing_info.items():
        if info['missing_rate'] == 100:
            completely_empty_cols.append(col)
    
    if completely_empty_cols:
        print(f'\n完全为空的列（将保留）: {completely_empty_cols}')
    else:
        print('\n没有完全为空的列')
    
    return missing_info, completely_empty_cols

def clean_missing_values(df, missing_info, completely_empty_cols):
    """清理缺失值（行级删除）"""
    original_rows = len(df)
    
    # 对于非完全为空的列，检测缺失值
    non_empty_cols = [col for col in df.columns if col not in completely_empty_cols]
    
    # 创建缺失值掩码
    missing_mask = pd.DataFrame(False, index=df.index, columns=df.columns)
    
    for col in non_empty_cols:
        # 检测NaN值
        missing_mask[col] |= df[col].isna()
        # 检测空字符串
        if df[col].dtype == 'object':
            missing_mask[col] |= (df[col] == '')
            missing_mask[col] |= (df[col] == 'null')
    
    # 找到包含任何缺失值的行
    rows_with_missing = missing_mask.any(axis=1)
    rows_to_drop = df[rows_with_missing].index
    
    print(f'检测到包含缺失值的行数: {len(rows_to_drop)}')
    
    # 显示一些包含缺失值的行的详细信息
    if len(rows_to_drop) > 0:
        print('包含缺失值的行示例（前5行）:')
        for i, idx in enumerate(rows_to_drop[:5]):
            missing_cols = []
            for col in non_empty_cols:
                if missing_mask.loc[idx, col]:
                    missing_cols.append(col)
            print(f'  行 {idx}: 缺失列 {missing_cols}')
    
    # 删除包含缺失值的行
    df_cleaned = df.drop(rows_to_drop)
    deleted_rows = len(rows_to_drop)
    
    print(f'删除的行数: {deleted_rows}')
    print(f'处理后数据形状: {df_cleaned.shape}')
    print(f'数据保留率: {len(df_cleaned)/original_rows*100:.2f}%')
    
    return df_cleaned

def convert_pregnancy_week_to_days(week_str):
    """将孕周字符串转换为天数"""
    if pd.isna(week_str):
        return np.nan
    
    # 处理格式如 '11w+6', '13w', '16W+1' 等
    week_str = str(week_str).strip().upper()  # 统一转为大写
    
    # 使用正则表达式解析
    pattern = r'(\d+)W(?:\+(\d+))?'
    match = re.match(pattern, week_str)
    
    if match:
        weeks = int(match.group(1))
        days = int(match.group(2)) if match.group(2) else 0
        total_days = weeks * 7 + days
        return total_days
    else:
        print(f'无法解析的孕周格式: {week_str}')
        return np.nan

def convert_pregnancy_weeks(df_cleaned):
    """转换孕周数据"""
    print("\n=== 第2步：孕周数据转换 ===")
    pregnancy_week_col = df_cleaned.iloc[:, 9].copy()  # 第9列（检测孕周）
    print(f'孕周列名: {df_cleaned.columns[9]}')
    
    # 转换前的样例
    print('转换前孕周数据样例:')
    sample_before = pregnancy_week_col.head(10).tolist()
    print(sample_before)
    
    # 执行转换
    pregnancy_days = pregnancy_week_col.apply(convert_pregnancy_week_to_days)
    
    # 转换后的样例
    print('转换后孕周数据样例（天数）:')
    sample_after = pregnancy_days.head(10).tolist()
    print(sample_after)
    
    # 检查转换结果
    conversion_success = pregnancy_days.notna().sum()
    conversion_failed = pregnancy_days.isna().sum()
    
    print(f'成功转换的记录数: {conversion_success}')
    print(f'转换失败的记录数: {conversion_failed}')
    
    if conversion_failed > 0:
        failed_values = pregnancy_week_col[pregnancy_days.isna()].unique()
        print(f'转换失败的原始值: {failed_values}')
    
    # 更新数据框中的孕周列
    df_cleaned.iloc[:, 9] = pregnancy_days
    
    print(f'转换后孕周数据范围: {pregnancy_days.min():.0f} - {pregnancy_days.max():.0f} 天')
    print(f'转换后孕周数据平均值: {pregnancy_days.mean():.1f} 天')
    
    return df_cleaned

def classify_y_concentration(concentration, threshold=0.04):
    """根据Y染色体浓度判断正常性"""
    if pd.isna(concentration):
        return '未知'
    elif concentration >= threshold:
        return '正常'
    else:
        return '不正常'

def add_y_concentration_classification(df_cleaned):
    """添加Y染色体浓度正常性判断列"""
    print("\n=== 第3步：添加Y染色体浓度正常性判断列 ===")
    
    # 获取Y染色体浓度数据
    y_concentration_col = 'Y染色体浓度'
    y_conc_data = df_cleaned[y_concentration_col]
    
    # 设置阈值（4% = 0.04）
    threshold = 0.04
    
    print(f'判断标准: Y染色体浓度 ≥ {threshold} (4%) 为正常，< {threshold} 为不正常')
    
    # 分析Y染色体浓度数据
    print(f'Y染色体浓度数据范围: {y_conc_data.min():.6f} - {y_conc_data.max():.6f}')
    print(f'Y染色体浓度平均值: {y_conc_data.mean():.6f}')
    
    # 添加新列
    df_cleaned['Y染色体浓度正常性'] = y_conc_data.apply(
        lambda x: classify_y_concentration(x, threshold)
    )
    
    print('\n新列添加完成！')
    
    # 统计结果
    result_counts = df_cleaned['Y染色体浓度正常性'].value_counts()
    print('\n分类统计结果:')
    for category, count in result_counts.items():
        percentage = count / len(df_cleaned) * 100
        print(f'  {category}: {count}例 ({percentage:.1f}%)')
    
    return df_cleaned

def generate_detailed_report(df_cleaned):
    """生成详细的处理结果报告"""
    print("\n=== 第4步：详细处理结果报告 ===")
    
    # Y染色体浓度正常性与胎儿健康状况交叉分析
    print('\nY染色体浓度正常性与胎儿健康状况交叉分析:')
    cross_table = pd.crosstab(df_cleaned['Y染色体浓度正常性'], df_cleaned['胎儿是否健康'], margins=True)
    print(cross_table)
    
    # 计算百分比
    cross_table_pct = pd.crosstab(df_cleaned['Y染色体浓度正常性'], df_cleaned['胎儿是否健康'], normalize='index') * 100
    print('\n各组内胎儿健康状况百分比:')
    print(cross_table_pct.round(1))
    
    # 显示异常案例的详细信息
    print('\n胎儿异常案例详细信息:')
    unhealthy_cases = df_cleaned[df_cleaned['胎儿是否健康'] == '否'][
        ['序号', 'Y染色体浓度', 'Y染色体浓度正常性', '染色体的非整倍体', '检测孕周']
    ]
    print(unhealthy_cases.to_string(index=False))
    
    # 基本统计信息
    print(f'\n处理后数据基本统计信息:')
    print(f'数据维度：{df_cleaned.shape[0]}行 × {df_cleaned.shape[1]}列')
    
    # 数值型列的统计
    numeric_cols = df_cleaned.select_dtypes(include=[np.number]).columns
    print(f'\n数值型列统计（共{len(numeric_cols)}列）：')
    for col in ['年龄', '身高', '体重', '检测孕周', 'Y染色体浓度']:
        if col in df_cleaned.columns:
            col_data = df_cleaned[col]
            print(f'   {col}：均值 {col_data.mean():.3f}, 标准差 {col_data.std():.3f}, '
                  f'范围 {col_data.min():.3f} - {col_data.max():.3f}')

def main():
    """主函数：执行完整的数据处理流程"""
    try:
        # 第0步：加载数据
        df_male = load_and_analyze_data()
        
        # 第1步：缺失值检测和处理
        missing_info, completely_empty_cols = detect_missing_values(df_male)
        df_cleaned = clean_missing_values(df_male, missing_info, completely_empty_cols)
        
        # 第2步：孕周数据转换
        df_cleaned = convert_pregnancy_weeks(df_cleaned)
        
        # 保存第一阶段处理结果
        df_cleaned.to_excel('男胎数据_已清洗.xlsx', index=False)
        print('\n第一阶段处理后的数据已保存为: 男胎数据_已清洗.xlsx')
        
        # 第3步：添加Y染色体浓度正常性判断
        df_cleaned = add_y_concentration_classification(df_cleaned)
        
        # 第4步：生成详细报告
        generate_detailed_report(df_cleaned)
        
        # 保存最终结果
        final_filename = '男胎数据_已清洗_含Y染色体判断.xlsx'
        df_cleaned.to_excel(final_filename, index=False)
        
        print(f'\n=== 处理完成 ===')
        print(f'最终数据文件: {final_filename}')
        print(f'数据维度: {df_cleaned.shape[0]}行 × {df_cleaned.shape[1]}列')
        print('所有处理步骤已完成！')
        
        return df_cleaned
        
    except Exception as e:
        print(f'处理过程中出现错误: {str(e)}')
        return None

if __name__ == "__main__":
    # 运行主程序
    result_df = main()
