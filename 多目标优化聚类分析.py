#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
男胎孕妇BMI多目标优化聚类分析
目标：为不同BMI组别确定最佳NIPT检测时点，最小化孕妇潜在风险
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def load_data():
    """加载和预处理数据"""
    print("=" * 60)
    print("多目标优化聚类分析 - 第2步：初始聚类分析")
    print("=" * 60)
    
    # 读取达标时间分析数据
    达标时间df = pd.read_excel('Y染色体浓度达标时间分析.xlsx')
    print(f"✅ 加载达标时间数据: {达标时间df.shape[0]}名孕妇")
    
    return 达标时间df

def determine_optimal_clusters(data, max_clusters=8):
    """确定最优聚类数量"""
    print(f"\n🔍 确定最优聚类数量 (测试1-{max_clusters}个聚类)")
    
    # 准备聚类特征：BMI和首次达标孕周
    features = data[['BMI', '首次达标孕周']].copy()
    
    # 标准化特征
    scaler = StandardScaler()
    features_scaled = scaler.fit_transform(features)
    
    # 计算不同聚类数的评估指标
    inertias = []
    silhouette_scores = []
    
    for k in range(2, max_clusters + 1):
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(features_scaled)
        
        inertia = kmeans.inertia_
        silhouette = silhouette_score(features_scaled, cluster_labels)
        
        inertias.append(inertia)
        silhouette_scores.append(silhouette)
        
        print(f"  k={k}: 惯性={inertia:.2f}, 轮廓系数={silhouette:.3f}")
    
    # 使用肘部法则和轮廓系数确定最优k
    # 找到轮廓系数最高的k
    best_k = np.argmax(silhouette_scores) + 2
    best_silhouette = max(silhouette_scores)
    
    print(f"\n✅ 推荐聚类数: k={best_k} (轮廓系数={best_silhouette:.3f})")
    
    return best_k, scaler, features

def perform_initial_clustering(data, k, scaler, features):
    """执行初始聚类分析"""
    print(f"\n🎯 执行初始聚类分析 (k={k})")
    
    # 标准化特征
    features_scaled = scaler.transform(features)
    
    # 执行K-means聚类
    kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
    cluster_labels = kmeans.fit_predict(features_scaled)
    
    # 添加聚类标签到数据
    data_clustered = data.copy()
    data_clustered['聚类标签'] = cluster_labels
    
    # 分析每个聚类的特征
    print(f"\n📊 聚类结果分析:")
    cluster_stats = []
    
    for i in range(k):
        cluster_data = data_clustered[data_clustered['聚类标签'] == i]
        
        stats = {
            '聚类': f'聚类{i}',
            '样本数': len(cluster_data),
            '占比': f"{len(cluster_data)/len(data)*100:.1f}%",
            'BMI均值': cluster_data['BMI'].mean(),
            'BMI范围': f"{cluster_data['BMI'].min():.1f}-{cluster_data['BMI'].max():.1f}",
            '达标时间均值(天)': cluster_data['首次达标孕周'].mean(),
            '达标时间均值(周)': cluster_data['首次达标周数'].mean(),
            '达标时间范围(周)': f"{cluster_data['首次达标周数'].min():.1f}-{cluster_data['首次达标周数'].max():.1f}"
        }
        
        cluster_stats.append(stats)
        
        print(f"  聚类{i}: {len(cluster_data)}人 ({len(cluster_data)/len(data)*100:.1f}%)")
        print(f"    BMI: {cluster_data['BMI'].mean():.1f}±{cluster_data['BMI'].std():.1f} "
              f"[{cluster_data['BMI'].min():.1f}-{cluster_data['BMI'].max():.1f}]")
        print(f"    达标时间: {cluster_data['首次达标周数'].mean():.1f}±{cluster_data['首次达标周数'].std():.1f}周 "
              f"[{cluster_data['首次达标周数'].min():.1f}-{cluster_data['首次达标周数'].max():.1f}周]")
    
    # 保存聚类统计结果
    cluster_stats_df = pd.DataFrame(cluster_stats)
    cluster_stats_df.to_excel('初始聚类统计结果.xlsx', index=False)
    
    return data_clustered, kmeans, cluster_stats_df

def calculate_risk_levels(data_clustered):
    """计算各聚类的风险等级分布"""
    print(f"\n⚠️ 风险等级分析:")
    
    def get_risk_level(weeks):
        if weeks <= 12:
            return '低风险(≤12周)'
        elif weeks <= 27:
            return '高风险(13-27周)'
        else:
            return '极高风险(≥28周)'
    
    data_clustered['风险等级'] = data_clustered['首次达标周数'].apply(get_risk_level)
    
    # 按聚类分析风险等级分布
    risk_analysis = []
    for cluster_id in sorted(data_clustered['聚类标签'].unique()):
        cluster_data = data_clustered[data_clustered['聚类标签'] == cluster_id]
        
        risk_counts = cluster_data['风险等级'].value_counts()
        total = len(cluster_data)
        
        risk_stats = {
            '聚类': f'聚类{cluster_id}',
            '总数': total,
            '低风险数': risk_counts.get('低风险(≤12周)', 0),
            '低风险率': f"{risk_counts.get('低风险(≤12周)', 0)/total*100:.1f}%",
            '高风险数': risk_counts.get('高风险(13-27周)', 0),
            '高风险率': f"{risk_counts.get('高风险(13-27周)', 0)/total*100:.1f}%",
            '极高风险数': risk_counts.get('极高风险(≥28周)', 0),
            '极高风险率': f"{risk_counts.get('极高风险(≥28周)', 0)/total*100:.1f}%"
        }
        
        risk_analysis.append(risk_stats)
        
        print(f"  聚类{cluster_id}:")
        print(f"    低风险: {risk_counts.get('低风险(≤12周)', 0)}人 ({risk_counts.get('低风险(≤12周)', 0)/total*100:.1f}%)")
        print(f"    高风险: {risk_counts.get('高风险(13-27周)', 0)}人 ({risk_counts.get('高风险(13-27周)', 0)/total*100:.1f}%)")
        print(f"    极高风险: {risk_counts.get('极高风险(≥28周)', 0)}人 ({risk_counts.get('极高风险(≥28周)', 0)/total*100:.1f}%)")
    
    risk_analysis_df = pd.DataFrame(risk_analysis)
    risk_analysis_df.to_excel('聚类风险等级分析.xlsx', index=False)
    
    return data_clustered, risk_analysis_df

def extract_bmi_thresholds(data_clustered, cluster_stats_df):
    """提取初始BMI分组阈值"""
    print(f"\n🎯 提取初始BMI分组阈值:")
    
    # 按BMI均值排序聚类
    cluster_stats_sorted = cluster_stats_df.sort_values('BMI均值')
    
    thresholds = []
    for i, row in cluster_stats_sorted.iterrows():
        cluster_id = int(row['聚类'].replace('聚类', ''))
        cluster_data = data_clustered[data_clustered['聚类标签'] == cluster_id]
        
        bmi_min = cluster_data['BMI'].min()
        bmi_max = cluster_data['BMI'].max()
        bmi_mean = cluster_data['BMI'].mean()
        
        threshold_info = {
            '聚类ID': cluster_id,
            'BMI下界': bmi_min,
            'BMI上界': bmi_max,
            'BMI均值': bmi_mean,
            '样本数': len(cluster_data),
            '建议阈值范围': f"{bmi_min:.1f}-{bmi_max:.1f}"
        }
        
        thresholds.append(threshold_info)
        
        print(f"  聚类{cluster_id}: BMI {bmi_min:.1f}-{bmi_max:.1f} (均值{bmi_mean:.1f}, {len(cluster_data)}人)")
    
    thresholds_df = pd.DataFrame(thresholds)
    thresholds_df.to_excel('初始BMI分组阈值.xlsx', index=False)
    
    print(f"\n✅ 初始BMI分组阈值已保存")
    
    return thresholds_df

def visualize_clustering_results(data_clustered):
    """可视化聚类结果"""
    print(f"\n📈 生成聚类可视化图表...")
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('男胎孕妇BMI聚类分析结果', fontsize=16, fontweight='bold')
    
    # 1. BMI vs 达标时间散点图
    ax1 = axes[0, 0]
    scatter = ax1.scatter(data_clustered['BMI'], data_clustered['首次达标周数'], 
                         c=data_clustered['聚类标签'], cmap='viridis', alpha=0.7)
    ax1.set_xlabel('孕妇BMI')
    ax1.set_ylabel('首次达标时间(周)')
    ax1.set_title('BMI vs Y染色体浓度首次达标时间')
    plt.colorbar(scatter, ax=ax1, label='聚类标签')
    
    # 2. 聚类样本数分布
    ax2 = axes[0, 1]
    cluster_counts = data_clustered['聚类标签'].value_counts().sort_index()
    ax2.bar(range(len(cluster_counts)), cluster_counts.values)
    ax2.set_xlabel('聚类标签')
    ax2.set_ylabel('样本数')
    ax2.set_title('各聚类样本数分布')
    ax2.set_xticks(range(len(cluster_counts)))
    
    # 3. BMI分布箱线图
    ax3 = axes[1, 0]
    bmi_by_cluster = [data_clustered[data_clustered['聚类标签']==i]['BMI'].values 
                      for i in sorted(data_clustered['聚类标签'].unique())]
    ax3.boxplot(bmi_by_cluster, labels=[f'聚类{i}' for i in range(len(bmi_by_cluster))])
    ax3.set_ylabel('BMI')
    ax3.set_title('各聚类BMI分布')
    
    # 4. 达标时间分布箱线图
    ax4 = axes[1, 1]
    time_by_cluster = [data_clustered[data_clustered['聚类标签']==i]['首次达标周数'].values 
                       for i in sorted(data_clustered['聚类标签'].unique())]
    ax4.boxplot(time_by_cluster, labels=[f'聚类{i}' for i in range(len(time_by_cluster))])
    ax4.set_ylabel('首次达标时间(周)')
    ax4.set_title('各聚类达标时间分布')
    
    plt.tight_layout()
    plt.savefig('初始聚类分析结果.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 聚类可视化图表已保存: 初始聚类分析结果.png")

def main():
    """主函数"""
    try:
        # 1. 加载数据
        data = load_data()
        
        # 2. 确定最优聚类数
        optimal_k, scaler, features = determine_optimal_clusters(data)
        
        # 3. 执行初始聚类
        data_clustered, kmeans_model, cluster_stats = perform_initial_clustering(
            data, optimal_k, scaler, features)
        
        # 4. 风险等级分析
        data_clustered, risk_analysis = calculate_risk_levels(data_clustered)
        
        # 5. 提取BMI阈值
        thresholds = extract_bmi_thresholds(data_clustered, cluster_stats)
        
        # 6. 可视化结果
        visualize_clustering_results(data_clustered)
        
        # 7. 保存完整结果
        data_clustered.to_excel('初始聚类完整结果.xlsx', index=False)
        
        print(f"\n" + "=" * 60)
        print("✅ 初始聚类分析完成！")
        print("📁 输出文件:")
        print("  - 初始聚类统计结果.xlsx")
        print("  - 聚类风险等级分析.xlsx") 
        print("  - 初始BMI分组阈值.xlsx")
        print("  - 初始聚类完整结果.xlsx")
        print("  - 初始聚类分析结果.png")
        print("=" * 60)
        
        return data_clustered, thresholds, optimal_k
        
    except Exception as e:
        print(f"❌ 初始聚类分析过程中出现错误: {str(e)}")
        return None, None, None

if __name__ == "__main__":
    result_data, result_thresholds, k = main()
