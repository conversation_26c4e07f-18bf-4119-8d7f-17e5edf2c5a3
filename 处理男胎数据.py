#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
C题男胎数据处理程序
功能：
1. 缺失值检测和处理（行级删除）
2. 孕周数据转换（周转天数）
3. Y染色体浓度正常性判断（≥4%为正常）
4. 生成处理报告和统计分析

使用方法：
python 处理男胎数据.py

要求：
- 当前目录下需要有 '附件.xlsx' 文件
- 需要安装 pandas, numpy 库
"""

import pandas as pd
import numpy as np
import re

def main():
    """主处理函数"""
    print("=" * 60)
    print("C题男胎数据处理程序")
    print("=" * 60)
    
    try:
        # 第1步：读取数据
        print("\n📂 第1步：读取数据")
        df = pd.read_excel('附件.xlsx', sheet_name='男胎检测数据')
        print(f"✅ 成功读取男胎数据: {df.shape[0]}行 × {df.shape[1]}列")
        
        # 第2步：缺失值处理
        print("\n🧹 第2步：缺失值检测和处理")
        original_rows = len(df)
        
        # 检测各种缺失值
        missing_mask = pd.DataFrame(False, index=df.index, columns=df.columns)
        missing_stats = {}
        
        for col in df.columns:
            # 检测NaN、空字符串、'null'
            nan_count = df[col].isna().sum()
            empty_count = (df[col] == '').sum() if df[col].dtype == 'object' else 0
            null_count = (df[col] == 'null').sum() if df[col].dtype == 'object' else 0
            
            total_missing = nan_count + empty_count + null_count
            if total_missing > 0:
                missing_stats[col] = total_missing
                missing_mask[col] = df[col].isna() | (df[col] == '') | (df[col] == 'null')
        
        print("缺失值统计:")
        for col, count in missing_stats.items():
            print(f"  {col}: {count}个缺失值 ({count/len(df)*100:.1f}%)")
        
        # 删除包含缺失值的行
        rows_to_drop = df[missing_mask.any(axis=1)].index
        df_clean = df.drop(rows_to_drop)
        
        print(f"✅ 删除了 {len(rows_to_drop)} 行包含缺失值的数据")
        print(f"✅ 保留数据: {len(df_clean)}行，保留率: {len(df_clean)/original_rows*100:.1f}%")
        
        # 第3步：孕周转换
        print("\n📅 第3步：孕周数据转换")
        
        def convert_week_to_days(week_str):
            """将孕周字符串转换为天数"""
            if pd.isna(week_str):
                return np.nan
            
            # 处理格式如 '11w+6', '13w', '16W+1' 等
            week_str = str(week_str).strip().upper()
            pattern = r'(\d+)W(?:\+(\d+))?'
            match = re.match(pattern, week_str)
            
            if match:
                weeks = int(match.group(1))
                days = int(match.group(2)) if match.group(2) else 0
                return weeks * 7 + days
            else:
                print(f"⚠️  无法解析的孕周格式: {week_str}")
                return np.nan
        
        # 转换第9列（检测孕周）
        pregnancy_col = df_clean.iloc[:, 9].copy()
        print(f"孕周列名: {df_clean.columns[9]}")
        print("转换前样例:", pregnancy_col.head(5).tolist())
        
        pregnancy_days = pregnancy_col.apply(convert_week_to_days)
        df_clean.iloc[:, 9] = pregnancy_days
        
        success_count = pregnancy_days.notna().sum()
        fail_count = pregnancy_days.isna().sum()
        
        print("转换后样例:", pregnancy_days.head(5).tolist())
        print(f"✅ 转换成功: {success_count}个，失败: {fail_count}个")
        print(f"✅ 孕周范围: {pregnancy_days.min():.0f}-{pregnancy_days.max():.0f}天 "
              f"({pregnancy_days.min()/7:.1f}-{pregnancy_days.max()/7:.1f}周)")
        
        # 第4步：Y染色体浓度判断
        print("\n🧬 第4步：Y染色体浓度正常性判断")
        
        y_conc = df_clean['Y染色体浓度']
        threshold = 0.04  # 4%
        
        print(f"判断标准: ≥{threshold} (4%) 为正常，<{threshold} 为不正常")
        print(f"Y染色体浓度范围: {y_conc.min():.6f}-{y_conc.max():.6f}")
        
        # 添加判断列
        df_clean['Y染色体浓度正常性'] = y_conc.apply(
            lambda x: '正常' if x >= threshold else '不正常'
        )
        
        # 统计结果
        normal_count = (df_clean['Y染色体浓度正常性'] == '正常').sum()
        abnormal_count = (df_clean['Y染色体浓度正常性'] == '不正常').sum()
        
        print(f"✅ 正常: {normal_count}例 ({normal_count/len(df_clean)*100:.1f}%)")
        print(f"✅ 不正常: {abnormal_count}例 ({abnormal_count/len(df_clean)*100:.1f}%)")
        
        # 第5步：保存结果
        print("\n💾 第5步：保存处理结果")
        output_file = '男胎数据_处理完成.xlsx'
        df_clean.to_excel(output_file, index=False)
        print(f"✅ 数据已保存为: {output_file}")
        print(f"✅ 最终数据维度: {df_clean.shape[0]}行 × {df_clean.shape[1]}列")
        
        # 第6步：生成统计报告
        print("\n📊 第6步：统计分析报告")
        print("-" * 40)
        
        # 基本统计
        print("基本统计信息:")
        print(f"  年龄: {df_clean['年龄'].min()}-{df_clean['年龄'].max()}岁，平均{df_clean['年龄'].mean():.1f}岁")
        print(f"  身高: {df_clean['身高'].min():.0f}-{df_clean['身高'].max():.0f}cm，平均{df_clean['身高'].mean():.1f}cm")
        print(f"  体重: {df_clean['体重'].min():.0f}-{df_clean['体重'].max():.0f}kg，平均{df_clean['体重'].mean():.1f}kg")
        print(f"  孕周: {pregnancy_days.min():.0f}-{pregnancy_days.max():.0f}天，平均{pregnancy_days.mean():.1f}天")
        
        # 胎儿健康状况
        health_stats = df_clean['胎儿是否健康'].value_counts()
        print(f"\n胎儿健康状况:")
        print(f"  健康: {health_stats.get('是', 0)}例 ({health_stats.get('是', 0)/len(df_clean)*100:.1f}%)")
        print(f"  异常: {health_stats.get('否', 0)}例 ({health_stats.get('否', 0)/len(df_clean)*100:.1f}%)")
        
        # Y染色体浓度与胎儿健康关系
        print(f"\nY染色体浓度与胎儿健康关系:")
        cross_table = pd.crosstab(df_clean['Y染色体浓度正常性'], df_clean['胎儿是否健康'])
        print(cross_table)
        
        # 异常案例详情
        abnormal_cases = df_clean[df_clean['胎儿是否健康'] == '否']
        if len(abnormal_cases) > 0:
            print(f"\n胎儿异常案例详情:")
            for _, case in abnormal_cases.iterrows():
                y_conc_pct = case['Y染色体浓度'] * 100
                print(f"  序号{case['序号']}: Y染色体浓度{case['Y染色体浓度']:.6f}({y_conc_pct:.2f}%) "
                      f"- {case['Y染色体浓度正常性']} - {case['染色体的非整倍体']}")
        
        print("\n" + "=" * 60)
        print("🎉 处理完成！所有步骤执行成功！")
        print("=" * 60)
        
        return df_clean
        
    except FileNotFoundError:
        print("❌ 错误: 找不到 '附件.xlsx' 文件")
        print("请确保文件在当前目录下")
        return None
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {str(e)}")
        return None

if __name__ == "__main__":
    # 运行主程序
    result = main()
    
    if result is not None:
        print(f"\n✅ 程序执行成功！")
        print(f"📁 输出文件: 男胎数据_处理完成.xlsx")
        print(f"📊 处理后数据: {result.shape[0]}行 × {result.shape[1]}列")
    else:
        print(f"\n❌ 程序执行失败！")
